const unsigned char yesafe_icon[] = {0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0xC8, 0x00, 0x00, 0x00, 0x64, 0x08, 0x06, 0x00, 0x00, 0x00, 0xC3, 0x86, 0x7F, 0x0B, 0x00, 0x00, 0x01, 0x8F, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0xED, 0xD4, 0x51, 0x6A, 0xC2, 0x40, 0x14, 0x40, 0xD1, 0x31, 0xBA, 0xA5, 0x6E, 0xBA, 0xAB, 0xEA, 0x42, 0xB4, 0x96, 0x42, 0x85, 0x30, 0x4C, 0xAE, 0xF9, 0xEF, 0x39, 0x30, 0xCC, 0x73, 0x48, 0x84, 0x44, 0xAE, 0x97, 0xAF, 0xCF, 0x8F, 0xCB, 0x18, 0x63, 0x1B, 0x63, 0xBC, 0xF6, 0x79, 0x7E, 0xAD, 0xEB, 0x62, 0xDE, 0xEF, 0xD7, 0x83, 0xCF, 0xBF, 0xEB, 0xF6, 0xE6, 0xEC, 0x76, 0xE2, 0xEC, 0xB6, 0xB8, 0x67, 0x75, 0xCD, 0x99, 0xEF, 0x3C, 0xBB, 0xB6, 0xC5, 0x73, 0xAD, 0xDE, 0xC7, 0xB6, 0x78, 0x77, 0xFB, 0x7D, 0x9E, 0xC7, 0x62, 0x5E, 0x79, 0xFE, 0x9D, 0x3D, 0x17, 0xF3, 0xF7, 0x62, 0xDE, 0xEF, 0xF3, 0x7A, 0xEC, 0x3E, 0x3F, 0x76, 0xFB, 0x3C, 0x9F, 0x59, 0xF7, 0xDD, 0x35, 0xF7, 0xF1, 0xFE, 0xEC, 0xBE, 0xB8, 0x67, 0x75, 0xCD, 0x99, 0xEF, 0x7C, 0xBC, 0x39, 0x9B, 0x9F, 0x65, 0x7E, 0xDE, 0xF9, 0x1D, 0xEC, 0xD7, 0x73, 0x9E, 0xB7, 0x83, 0x1F, 0x06, 0xFE, 0xBD, 0xF1, 0xF7, 0xAF, 0x06, 0x1C, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x04, 0x81, 0x40, 0x10, 0x08, 0x1C, 0x19, 0x63, 0xFC, 0x00, 0x24, 0xFC, 0x61, 0x88, 0xC4, 0xD1, 0x39, 0xE9, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82};

const unsigned char nosafe_icon[] = {0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0xC8, 0x00, 0x00, 0x00, 0x64, 0x08, 0x06, 0x00, 0x00, 0x00, 0xC3, 0x86, 0x7F, 0x0B, 0x00, 0x00, 0x05, 0x7D, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0xED, 0x9D, 0x0D, 0x6A, 0xDC, 0x30, 0x10, 0x85, 0xED, 0x64, 0xAF, 0xD4, 0x93, 0xF4, 0xFE, 0xD0, 0x83, 0x24, 0x71, 0x69, 0xD9, 0x05, 0x31, 0x8C, 0x6C, 0xEB, 0x8D, 0x5A, 0x3F, 0x87, 0xEF, 0x83, 0x65, 0x15, 0x37, 0x09, 0xCE, 0x56, 0xCF, 0xF3, 0xA3, 0xD1, 0x68, 0xFD, 0xF1, 0xEB, 0xE7, 0xBA, 0x2C, 0xCB, 0xDB, 0xB2, 0x2C, 0xAF, 0xF7, 0x38, 0x7E, 0xBD, 0xDE, 0x93, 0x71, 0xFB, 0xFE, 0xDE, 0xF9, 0xFA, 0xCF, 0xEB, 0x71, 0x70, 0xED, 0x71, 0xE2, 0xDA, 0x23, 0xF9, 0x99, 0xEC, 0x7B, 0xCE, 0xFC, 0xCE, 0xB3, 0xAF, 0xB7, 0xE4, 0xEF, 0x7A, 0x7E, 0x1E, 0xDB, 0x73, 0xBC, 0xFE, 0x19, 0xBF, 0x35, 0xE3, 0xF8, 0x79, 0xAE, 0xC9, 0x78, 0x49, 0xC6, 0x19, 0xDB, 0xF3, 0xDA, 0x96, 0x8C, 0xBF, 0x92, 0x71, 0xFB, 0xFE, 0x7C, 0xAD, 0xAF, 0xF1, 0x67, 0x73, 0xFD, 0xB3, 0x79, 0x8F, 0xE3, 0x33, 0xAF, 0x8F, 0xE6, 0x7B, 0x3E, 0x96, 0xE3, 0x6B, 0x1F, 0xC9, 0xCF, 0x64, 0xDF, 0x73, 0xE6, 0x77, 0x7E, 0x1E, 0x5C, 0x8B, 0x7F, 0x4B, 0xFC, 0x7B, 0xB3, 0xCF, 0xE3, 0x2B, 0xF9, 0xEC, 0xFE, 0x8E, 0x1F, 0x9D, 0xFF, 0x18, 0x38, 0xE4, 0x35, 0xA7, 0xB7, 0xE6, 0x1B, 0x37, 0xB3, 0x8F, 0xAD, 0xA7, 0x3B, 0x38, 0xCB, 0x1B, 0x9F, 0x54, 0x15, 0x97, 0x49, 0xB8, 0x26, 0x63, 0x37, 0xC1, 0xDE, 0x0F, 0x04, 0x32, 0x4C, 0x9C, 0x74, 0x9B, 0x89, 0x48, 0x10, 0xC3, 0xBF, 0x00, 0x81, 0xC8, 0xC4, 0xB0, 0x60, 0x31, 0xB2, 0x26, 0x88, 0x65, 0x16, 0xC4, 0x20, 0xC3, 0xEC, 0xC5, 0xD5, 0x2F, 0x6B, 0xE2, 0x32, 0x41, 0xA3, 0xAB, 0x45, 0x4C, 0x32, 0x0A, 0x16, 0x44, 0xA6, 0x17, 0x9C, 0x3B, 0x3D, 0xBD, 0xB1, 0x24, 0x55, 0xB0, 0x20, 0x12, 0xD9, 0xC4, 0x73, 0x9C, 0x8C, 0xAB, 0x51, 0x8C, 0x74, 0x4F, 0xB0, 0x20, 0x12, 0xAB, 0x99, 0x2B, 0x15, 0xC9, 0x32, 0x5A, 0xA0, 0x80, 0x40, 0x24, 0x5A, 0x9F, 0xDE, 0x51, 0x24, 0xD1, 0x6A, 0xE0, 0x6A, 0xA9, 0xE0, 0x62, 0x95, 0x71, 0x59, 0x73, 0x88, 0x62, 0xDD, 0xC2, 0xBF, 0x81, 0x02, 0x16, 0x44, 0xA2, 0x15, 0xC5, 0x66, 0xE2, 0xE7, 0x67, 0xF7, 0x80, 0x30, 0xAA, 0x60, 0x41, 0x24, 0x62, 0xDA, 0x74, 0x33, 0x71, 0x63, 0xB2, 0xFB, 0x82, 0x0A, 0x58, 0x10, 0x89, 0xDE, 0x93, 0xD9, 0x6D, 0xA1, 0x70, 0xC5, 0x8A, 0x14, 0x41, 0x20, 0x32, 0xAB, 0xF9, 0x13, 0x7B, 0x33, 0xB2, 0x6C, 0xF7, 0x05, 0x81, 0x48, 0x64, 0x59, 0x2C, 0xA7, 0xA7, 0x75, 0x56, 0x51, 0x0F, 0x0A, 0x08, 0x44, 0x22, 0x4E, 0x3A, 0x87, 0x27, 0x75, 0x26, 0x06, 0x2C, 0x48, 0x15, 0x04, 0x32, 0xCC, 0x5E, 0x2A, 0xF5, 0xCA, 0xC9, 0xD8, 0x13, 0x03, 0x16, 0xA4, 0x02, 0x02, 0x19, 0x26, 0x9B, 0x70, 0x0E, 0xE9, 0xD5, 0xB5, 0x79, 0xB9, 0x2F, 0x64, 0xDE, 0x07, 0x04, 0x22, 0x91, 0x55, 0xC7, 0x5E, 0x5D, 0xB0, 0x18, 0x83, 0xF2, 0x36, 0x0E, 0x41, 0x24, 0x2A, 0x08, 0x44, 0x62, 0x0D, 0x93, 0xD1, 0xB5, 0x20, 0x90, 0x32, 0xF7, 0x2A, 0x08, 0x44, 0x26, 0x16, 0x04, 0xBA, 0x3D, 0xA5, 0xB1, 0x1A, 0x33, 0x40, 0x20, 0x53, 0x70, 0xB1, 0x20, 0xBD, 0x2A, 0x5E, 0xC4, 0xA2, 0x82, 0x40, 0x64, 0x9C, 0x32, 0x58, 0x2F, 0x08, 0xCE, 0x67, 0x83, 0x40, 0x86, 0xC9, 0x16, 0x06, 0x1D, 0x2C, 0xC8, 0x51, 0x6B, 0x2D, 0x50, 0xA0, 0x58, 0x71, 0x98, 0x2C, 0xA5, 0xEB, 0xB0, 0x20, 0xD7, 0x5B, 0xDD, 0x87, 0x0A, 0x58, 0x90, 0x6F, 0x05, 0x5B, 0x6C, 0x67, 0x83, 0x05, 0x91, 0xD8, 0x5B, 0xB1, 0xBE, 0xCA, 0x92, 0xAC, 0x49, 0xDA, 0x39, 0xBE, 0xC3, 0x28, 0x08, 0x44, 0x26, 0x66, 0x89, 0x9C, 0x5A, 0xFD, 0xDC, 0x61, 0x8D, 0xE6, 0x1E, 0xE0, 0x62, 0x49, 0xB8, 0x2E, 0x0A, 0x66, 0xF1, 0x07, 0xE2, 0xA8, 0x80, 0x40, 0x64, 0x1C, 0x2B, 0x65, 0xB3, 0x40, 0x9D, 0x8A, 0xDE, 0x0A, 0x08, 0x44, 0x66, 0x4D, 0xFC, 0x7B, 0x97, 0xC5, 0x42, 0xB2, 0x58, 0xB3, 0x40, 0x20, 0x32, 0xF1, 0xC9, 0xEC, 0x92, 0x41, 0x8A, 0x56, 0x04, 0x91, 0x54, 0x40, 0x20, 0x32, 0x4E, 0x95, 0xBC, 0x2D, 0xD9, 0xE2, 0x25, 0x22, 0x51, 0x41, 0x20, 0x32, 0xAE, 0xCD, 0xD9, 0xB2, 0xE3, 0x19, 0x88, 0x41, 0x54, 0x10, 0xC8, 0x30, 0x7B, 0xD6, 0xC2, 0xAD, 0xDC, 0x64, 0x6B, 0x62, 0x25, 0x50, 0x40, 0x20, 0xC3, 0xF4, 0xDC, 0x16, 0x87, 0x02, 0x41, 0x37, 0xC1, 0xDE, 0x1F, 0x04, 0x22, 0x91, 0xB9, 0x2D, 0x4E, 0x99, 0x23, 0x84, 0x31, 0x0B, 0x04, 0x22, 0xB1, 0x26, 0xAE, 0x8B, 0xD3, 0xA4, 0x8C, 0x27, 0x5F, 0x11, 0x83, 0xA8, 0x20, 0x10, 0x99, 0x5E, 0x8A, 0xD7, 0xA9, 0xE4, 0xC4, 0xF1, 0xBE, 0xEE, 0x05, 0x02, 0x19, 0x26, 0x06, 0xE9, 0x71, 0xD5, 0xFA, 0x0A, 0xCE, 0x74, 0x55, 0xC1, 0xED, 0x52, 0x40, 0x20, 0xC3, 0x38, 0xD7, 0x61, 0xB5, 0x5F, 0xB3, 0x9A, 0x3E, 0x03, 0x04, 0x22, 0x13, 0x5D, 0x18, 0x97, 0x15, 0xF4, 0xA3, 0x6B, 0x30, 0x02, 0x02, 0x99, 0x82, 0xCB, 0x13, 0x3B, 0x26, 0x0D, 0xDC, 0x8E, 0x67, 0xB8, 0x1F, 0x08, 0x44, 0x62, 0x6F, 0xB2, 0x5D, 0xDD, 0x7E, 0x74, 0xE9, 0x6C, 0x9E, 0x02, 0x05, 0x36, 0x4C, 0x49, 0xF4, 0x9A, 0x35, 0x38, 0xF7, 0xC6, 0x42, 0x24, 0x0A, 0x58, 0x90, 0x12, 0xAE, 0x2E, 0x0C, 0x56, 0x63, 0x16, 0x08, 0x44, 0x26, 0xA6, 0x76, 0x9D, 0x26, 0xA4, 0x53, 0xF2, 0xE0, 0xDE, 0xE0, 0x62, 0x0D, 0xB3, 0x25, 0x19, 0x2C, 0x17, 0x10, 0xC3, 0x6C, 0x10, 0xC8, 0x30, 0xCE, 0x3D, 0x79, 0x7B, 0x47, 0xC2, 0x21, 0x1C, 0x15, 0x5C, 0xAC, 0x12, 0x8E, 0xA9, 0xD3, 0x4C, 0xB4, 0xA4, 0x78, 0x55, 0xB0, 0x20, 0x12, 0xBD, 0x09, 0xE7, 0x70, 0x98, 0x27, 0x99, 0xAB, 0x99, 0x60, 0x41, 0x24, 0x62, 0x35, 0xAF, 0x4B, 0x36, 0x8B, 0x33, 0x0A, 0x67, 0x83, 0x05, 0x91, 0xD9, 0x2B, 0x5A, 0xBC, 0x1A, 0x2C, 0xC7, 0x2C, 0xB0, 0x20, 0x12, 0xAE, 0x3B, 0xF7, 0x7A, 0xF5, 0x58, 0x58, 0x11, 0x15, 0x04, 0x52, 0xC6, 0xE9, 0x08, 0x84, 0x17, 0xB1, 0x36, 0x0C, 0x8B, 0xA2, 0x82, 0x40, 0x64, 0xB2, 0x80, 0xDC, 0xB5, 0xD4, 0x04, 0x0B, 0xA2, 0x82, 0x40, 0x24, 0x7A, 0x6B, 0x21, 0x2E, 0x5D, 0x4D, 0x7A, 0x49, 0x04, 0x18, 0x05, 0x81, 0x4C, 0xE5, 0x6A, 0x37, 0x0B, 0x8B, 0x31, 0x1B, 0x04, 0x22, 0x11, 0x8F, 0x17, 0x68, 0x71, 0x5C, 0x59, 0x8F, 0x63, 0x38, 0x0B, 0x02, 0x91, 0x71, 0x6B, 0x58, 0x7D, 0x74, 0x9D, 0xC6, 0x0D, 0x0A, 0x08, 0x64, 0x18, 0x97, 0x46, 0x0D, 0x2D, 0x59, 0xE5, 0x6E, 0x76, 0xFC, 0x01, 0xB1, 0xC8, 0x28, 0x08, 0x64, 0x98, 0x5E, 0x29, 0xF9, 0xD5, 0x16, 0xA5, 0x27, 0x04, 0x44, 0x51, 0x01, 0x81, 0x48, 0x64, 0x99, 0x2B, 0x87, 0xD5, 0x74, 0x87, 0x5A, 0xB0, 0xEF, 0x05, 0xA5, 0x26, 0x32, 0xD9, 0x93, 0xD9, 0xA1, 0x37, 0x2F, 0x3D, 0xB1, 0x66, 0x82, 0x40, 0xCA, 0xB8, 0xD5, 0x61, 0x65, 0xF7, 0x81, 0x40, 0x54, 0x70, 0xB1, 0x24, 0x9C, 0x53, 0xBB, 0x8E, 0x96, 0xED, 0xBE, 0x20, 0x10, 0x89, 0x18, 0x77, 0x38, 0x36, 0xAE, 0x8E, 0xAB, 0xE9, 0x88, 0x44, 0x01, 0x81, 0xC8, 0xB8, 0x9F, 0xC5, 0x41, 0xC1, 0xE2, 0x0C, 0x10, 0xC8, 0x30, 0x59, 0xF6, 0xCA, 0x6D, 0xA3, 0x54, 0x9B, 0x72, 0xDE, 0x4C, 0xEE, 0xF1, 0x9E, 0x10, 0xA4, 0x0F, 0xD3, 0x4B, 0xEB, 0x5E, 0x19, 0xA8, 0xF7, 0x62, 0x22, 0xFA, 0x63, 0x55, 0xC1, 0x82, 0xC8, 0x64, 0xDB, 0x5B, 0x5D, 0xEE, 0x89, 0xD8, 0x63, 0x16, 0x08, 0xA4, 0x84, 0x5B, 0x09, 0x47, 0x16, 0x17, 0x61, 0x41, 0x2A, 0x20, 0x90, 0x61, 0x1C, 0xBB, 0x86, 0xEC, 0xA5, 0x76, 0xB1, 0x20, 0x15, 0x10, 0xC8, 0x30, 0xBD, 0x82, 0xC0, 0x2B, 0xD9, 0x5B, 0x1C, 0xC4, 0x82, 0x54, 0x40, 0x20, 0x65, 0x5C, 0x77, 0xEE, 0x61, 0x39, 0x66, 0x80, 0x40, 0xA6, 0xE0, 0x54, 0xA8, 0xD8, 0xFB, 0x1A, 0x14, 0x10, 0x48, 0x19, 0x97, 0x94, 0xEA, 0x5E, 0xAA, 0x17, 0x54, 0x58, 0x07, 0x91, 0xD8, 0x3A, 0x6E, 0x95, 0x53, 0xC1, 0xA2, 0x63, 0xF7, 0xF9, 0xFB, 0x81, 0x40, 0x64, 0xDC, 0xAA, 0x78, 0x23, 0x88, 0x63, 0x06, 0xB8, 0x58, 0x12, 0xBD, 0xDD, 0x84, 0x71, 0x7C, 0x35, 0x94, 0x98, 0x54, 0xC1, 0x82, 0x94, 0x70, 0x3E, 0xFE, 0x00, 0xEB, 0x31, 0x03, 0x2C, 0xC8, 0x14, 0x9C, 0x9A, 0x56, 0xC7, 0x56, 0x3F, 0x58, 0x8F, 0x0A, 0x58, 0x10, 0x99, 0xAD, 0x53, 0xEF, 0xE4, 0x70, 0x36, 0x08, 0x8B, 0x84, 0xB3, 0xC0, 0x82, 0x94, 0x70, 0x2D, 0x3B, 0x21, 0xC5, 0x3B, 0x0B, 0x04, 0x52, 0x22, 0xEE, 0xBB, 0x70, 0x10, 0x49, 0xAF, 0x6F, 0x17, 0x62, 0x51, 0x40, 0x20, 0x12, 0x59, 0x53, 0xB6, 0xC5, 0x60, 0x12, 0x66, 0x87, 0x77, 0x12, 0xB4, 0x57, 0x40, 0x20, 0x32, 0x8E, 0x1D, 0xD4, 0xA3, 0xCB, 0x47, 0x47, 0xC5, 0x2A, 0x08, 0x44, 0xC2, 0x71, 0xB3, 0x54, 0xA4, 0x75, 0xB5, 0x70, 0xAF, 0x54, 0x10, 0x88, 0x44, 0xCF, 0xC5, 0xBA, 0xF2, 0x49, 0xBD, 0xB7, 0x27, 0x04, 0x0B, 0xA2, 0x82, 0x40, 0x24, 0xB2, 0xAD, 0xAD, 0x8B, 0x49, 0x8A, 0x77, 0xD9, 0xB9, 0x3F, 0x18, 0x05, 0x81, 0xC8, 0xC4, 0x2A, 0xDE, 0x5E, 0x01, 0xE3, 0xFF, 0x64, 0xCF, 0xF5, 0xC3, 0xCD, 0x52, 0x40, 0x20, 0x32, 0x59, 0xA1, 0xE2, 0xD5, 0x29, 0xD5, 0x98, 0xAD, 0xA2, 0xCB, 0x7B, 0x15, 0x04, 0x22, 0xD1, 0xCB, 0x16, 0xB9, 0xDC, 0x5B, 0x2F, 0x46, 0x82, 0x51, 0x10, 0x88, 0x44, 0x74, 0xA5, 0x1C, 0xD2, 0xBD, 0x67, 0x4E, 0x99, 0x82, 0x51, 0xA8, 0xC5, 0x92, 0xE9, 0xED, 0x07, 0x71, 0x69, 0x1E, 0x17, 0x93, 0x07, 0x08, 0x45, 0x01, 0x0B, 0x52, 0xA2, 0xD7, 0x87, 0xCA, 0xC1, 0x8A, 0x38, 0xD6, 0x89, 0xDD, 0x0F, 0x04, 0x32, 0xCC, 0xD1, 0xDA, 0xC2, 0x95, 0x7E, 0x7F, 0xCC, 0xA4, 0x11, 0x7F, 0x54, 0x41, 0x20, 0xC3, 0xC4, 0x89, 0xE7, 0xB6, 0x27, 0x3D, 0xCB, 0xA4, 0x21, 0x14, 0x15, 0x04, 0x22, 0x71, 0x07, 0xFF, 0x9E, 0x14, 0x6F, 0x99, 0x65, 0x59, 0x7E, 0x03, 0x4B, 0x47, 0xB8, 0x56, 0x9D, 0xEA, 0x71, 0x15, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82};
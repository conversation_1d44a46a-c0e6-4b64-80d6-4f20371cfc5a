#include "Custom.h"

#include <string>
using namespace ImGui;
int rotation_start_index;

static float currentWidth = 0.0f;
static float currentHeight = 0.0f;
static float maxWidth = 1030.0f;  // 目标宽度
static float maxHeight = 780.0f; // 目标高度
static float animationSpeedWidth = 13.0f;  // 宽度每帧增加的像素数
static float animationSpeedHeight = 9.0f;  // 高度每帧增加的像素数
static bool windowOpened = false; // 窗口是否已打开标志
static bool closingAnimation = false;  // 是否正在执行关闭动画
static bool shouldDisplay = false;


ImU32 c_透明=IM_COL32(0, 0, 0, 0);
ImU32 c_fafafa=IM_COL32(255, 255, 255, 255);
ImU32 c_cccccc=IM_COL32(204, 204, 204, 255);
ImU32 c_c2c2c2 = IM_COL32(194, 194, 194, 255);
ImU32 c_23292e=IM_COL32(35, 41, 46, 255);
ImU32 c_4023292e=IM_COL32(35, 41, 46, 125);
ImU32 c_2f363c=IM_COL32(47, 54, 60, 255);
ImU32 c_402f363c=IM_COL32(47, 54, 60, 125);
ImU32 c_DAB123 = IM_COL32(218, 177, 35, 255);
ImVec4 半透明黑色ImVec4 = ImVec4(0.184314f, 0.211765f, 0.235294f, 0.5f);
ImVec4 半透明黑色ImVec480 = ImVec4(0.184314f, 0.211765f, 0.235294f, 0.75f);
ImVec4 ImVec423292e = ImVec4(0.137255f, 0.160784f, 0.180392f, 1.0f);
ImVec4 ImVec48023292e = ImVec4(0.137255f, 0.160784f, 0.180392f, 0.75f);
ImVec4 ImVec44023292e = ImVec4(0.137255f, 0.160784f, 0.180392f, 0.5f);

bool Custom::Begin(const char* name, bool* p_open, ImGuiWindowFlags flags) {
    if (!windowOpened && *p_open) {
        currentWidth = 0.0f;
        currentHeight = 0.0f;
        windowOpened = true;
        closingAnimation = false;
    } else if (!*p_open && windowOpened && !closingAnimation) {
        closingAnimation = true;
    }

    if (!closingAnimation && *p_open) {
        if (currentWidth < maxWidth) {
            currentWidth += animationSpeedWidth;
        }
        if (currentHeight < maxHeight) {
            currentHeight += animationSpeedHeight;
        }
    }

    if (closingAnimation) {
        currentWidth -= animationSpeedWidth;
        currentHeight -= animationSpeedHeight;
        if (currentWidth <= 0 || currentHeight <= 0) {
            currentWidth = 0;
            currentHeight = 0;
            windowOpened = false;
            closingAnimation = false;
            *p_open = false; // 关闭动画完成后，确保外部的 p_open 标记为 false
        }
    }

    currentWidth = ImClamp(currentWidth, 0.0f, maxWidth);
    currentHeight = ImClamp(currentHeight, 0.0f, maxHeight);
    ImGui::SetNextWindowSize(ImVec2(currentWidth, currentHeight), ImGuiCond_Always);

    bool shouldDisplay = false;
    if (currentWidth > 0 && currentHeight > 0) {
    ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.0f, 0.0f, 1.0f, 1.0f));  // 推送蓝色文本
        shouldDisplay = ImGui::Begin(name, p_open, flags);
        ImGui::PopStyleColor();
    }

    if (!shouldDisplay && closingAnimation) {
        // 如果窗口正在关闭并且不应该显示，则结束 ImGui 窗口绘制
        ImGui::End();
    }

    return shouldDisplay;
}

bool Custom::Begin(const char* name, bool* p_open) {
    if (!windowOpened && *p_open) {
        currentWidth = 0.0f;
        currentHeight = 0.0f;
        windowOpened = true;
        closingAnimation = false;
    } else if (!*p_open && windowOpened && !closingAnimation) {
        closingAnimation = true;
    }

    if (!closingAnimation && *p_open) {
        if (currentWidth < maxWidth) {
            currentWidth += animationSpeedWidth;
        }
        if (currentHeight < maxHeight) {
            currentHeight += animationSpeedHeight;
        }
    }

    if (closingAnimation) {
        currentWidth -= animationSpeedWidth;
        currentHeight -= animationSpeedHeight;
        if (currentWidth <= 0 || currentHeight <= 0) {
            currentWidth = 0;
            currentHeight = 0;
            windowOpened = false;
            closingAnimation = false;
            *p_open = false; // 关闭动画完成后，确保外部的 p_open 标记为 false
        }
    }

    currentWidth = ImClamp(currentWidth, 0.0f, maxWidth);
    currentHeight = ImClamp(currentHeight, 0.0f, maxHeight);
    ImGui::SetNextWindowSize(ImVec2(currentWidth, currentHeight), ImGuiCond_Always);

    bool shouldDisplay = false;
    if (currentWidth > 0 && currentHeight > 0) {
    ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.0f, 0.0f, 1.0f, 1.0f));  // 推送蓝色文本
        shouldDisplay = ImGui::Begin(name, p_open);
        ImGui::PopStyleColor();
    }

    if (!shouldDisplay && closingAnimation) {
        // 如果窗口正在关闭并且不应该显示，则结束 ImGui 窗口绘制
        ImGui::End();
    }

    return shouldDisplay;
}

void Custom::End() {
    if (ImGui::GetCurrentContext()->WithinEndChild) {
        ImGui::End(); // 确保只有当Begin成功时才调用End
    }
}

void Custom::ImRotateStart() {
    rotation_start_index = ImGui::GetWindowDrawList()->VtxBuffer.Size;
}
 
ImVec2 Custom::ImRotationCenter() {
    ImVec2 l(FLT_MAX, FLT_MAX), u(-FLT_MAX, -FLT_MAX);
    const auto& buf = ImGui::GetWindowDrawList()->VtxBuffer;
    for (int i = rotation_start_index; i < buf.Size; i++)
        l = ImMin(l, buf[i].pos), u = ImMax(u, buf[i].pos);
    return ImVec2((l.x + u.x) / 2, (l.y + u.y) / 2);
}

void Custom::ImRotateEnd(float rad, ImVec2 center) {
    float s = sin(rad), c = cos(rad);
    center = ImRotate(center, s, c) - center;
    auto& buf = ImGui::GetWindowDrawList()->VtxBuffer;
    for (int i = rotation_start_index; i < buf.Size; i++)
    	buf[i].pos = ImRotate(buf[i].pos, s, c) - center;
}

bool Custom::tab(const char* icon, const char* label, bool selected) {
    ImGuiWindow* window = GetCurrentWindow();
    ImGuiID id = window->GetID(label);
    ImVec2 p = window->DC.CursorPos;
    ImVec2 size({ window->Size.x, 85});
    ImRect bb(p, p + size);
    ItemSize(bb);
    ItemAdd(bb, id);
    bool hovered, held;
    bool pressed = ButtonBehavior(bb, id, &hovered, &held);
    float anim = ImTricks::Animations::FastFloatLerp(std::string(label).append( "tab.anim"), selected, 0, 1, 0.04);
    auto col = ImTricks::Animations::FastColorLerp(GetColorU32(ImGuiCol_TextDisabled), GetColorU32(ImGuiCol_Scheme), anim);
    if (pressed)
        Content_Anim = 0;
    static float line_pos = 0;
    line_pos = ImLerp(line_pos, selected ? bb.Min.y - window->Pos.y : line_pos, 0.04);
    window->DrawList->AddRectFilled({bb.Max.x - 3, window->Pos.y + line_pos}, {bb.Max.x, window->Pos.y + line_pos + size.y}, GetColorU32(ImGuiCol_Scheme, anim), 2, ImDrawFlags_RoundCornersLeft);
    PushStyleColor(ImGuiCol_Text, col.Value);
    PushFont(GetIO().Fonts->Fonts[1]);
    RenderText({bb.Min.x + 32, bb.GetCenter().y - CalcTextSize(icon).y / 2}, icon);
    PopFont();
    RenderText({bb.Min.x + 133, bb.GetCenter().y - CalcTextSize(label).y / 2}, label);
    PopStyleColor();
    return pressed;
}

bool Custom::subtab(const char* label, bool selected) {
    ImGuiWindow* window = GetCurrentWindow();
    ImGuiID id = window->GetID(label);
    ImVec2 p = window->DC.CursorPos;
    ImVec2 size({CalcTextSize(label, 0, true).x + 10, window->Size.y});
    ImRect bb(p, {p.x + size.x, p.y + size.y});
    ItemSize(bb);
    ItemAdd(bb, id);
    bool hovered, held;
    bool pressed = ButtonBehavior(bb, id, &hovered, &held);
    float anim = ImTricks::Animations::FastFloatLerp(std::string(label).append("subtab.anim"), selected, 0, 1, 0.04);
    auto col = ImTricks::Animations::FastColorLerp(GetColorU32(ImGuiCol_TextDisabled), GetColorU32(ImGuiCol_Scheme), anim);
    window->DrawList->AddRectFilled({bb.Min.x, bb.Max.y - 3}, bb.Max, GetColorU32(ImGuiCol_Scheme, anim), 2, ImDrawFlags_RoundCornersTop);
    PushStyleColor(ImGuiCol_Text, col.Value);
    RenderText(bb.GetCenter() - CalcTextSize(label, 0, true) / 2, label);
    PopStyleColor();
    return pressed;
}

void Custom::begin_child(const char* name, ImVec2 size) {
    ImGuiWindow* window = GetCurrentWindow();
    ImVec2 Pos = window->DC.CursorPos;
    BeginChild(std::string(name).append("mainnoe").c_str(), size, false, ImGuiWindowFlags_NoScrollbar);
    GetWindowDrawList()->AddText(NULL, 40, {Pos.x + 15, Pos.y + 15}, GetColorU32(ImGuiCol_Text), name);
    SetCursorPosY(70);
    BeginChild(std::string(name).append("maintwo").c_str(), {size.x, size.y == 0 ? size.y : size.y - 90}, false, ImGuiWindowFlags_NoBackground | ImGuiWindowFlags_NoScrollbar);
    SetCursorPosX(15);
    BeginGroup();
    PushStyleVar(ImGuiStyleVar_ItemSpacing, {10, 15});
}

void Custom::end_child() {
    PopStyleVar();
    EndGroup();
    EndChild();
    EndChild();
}

bool Custom::collapse_button(bool collapsed) {
    BeginChild("##123", {82, 82}, false, ImGuiWindowFlags_NoBackground);
    ImGuiWindow* window = GetCurrentWindow();
    ImGuiID id = window->GetID("collapse_button");
    ImVec2 p = window->DC.CursorPos;
    ImVec2 size(GetWindowSize());
    ImRect bb(p, p + size);
    ItemSize(bb);
    ItemAdd(bb, id);
    bool hovered, held;
    bool pressed = ButtonBehavior(bb, id, &hovered, &held);
    float anim = ImTricks::Animations::FastFloatLerp("collapse_button.anim", collapsed, 0, 1, 0.04);
    ImRotateStart();
    RenderArrow(window->DrawList, bb.GetCenter() - ImVec2(2 + 2 * !anim, 6), GetColorU32(ImGuiCol_Text), ImGuiDir_Down, 35);
    ImRotateEnd(3.14 * anim);
    EndChild();
    return pressed;
}



void Custom::RadioGroup(const char* label, int* selectedIndex, const char* options[], int numOptions, ImVec2 width) {
    ImGui::PushID(label);
    const ImGuiStyle& style = ImGui::GetStyle();
    const ImVec2 startPos = ImGui::GetCursorScreenPos();
    const float buttonSpacing = style.ItemSpacing.x;
    const ImVec2 buttonStartPos = ImVec2(startPos.x , startPos.y + style.FramePadding.y);
    //const ImU32 selectedColor = ImGui::GetColorU32(c_fafafa);
    const ImU32 unselectedTextColor = c_fafafa;
    const ImU32 selectedTextColor = c_2f363c;
    const float buttonWidth = (width.x - buttonSpacing * (numOptions - 1)) / numOptions;
    const float borderRadius = 23.0f; // 圆角半径
    const ImU32 borderColor = IM_COL32(255, 255, 255, 255); // 自定义边框颜色
    const float borderWidth = 0.f; // 自定义边框宽度
    ImGui::PushStyleVar(ImGuiStyleVar_FrameBorderSize, borderWidth);
    ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, borderRadius);
    ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(0.0f, style.ItemSpacing.y));
    
    
    for (int i = 0; i < numOptions; ++i) {
        if (i > 0) {
            ImGui::SameLine();
        }
        if (*selectedIndex == i) {
        ImVec4 lerpedColor = ImLerp(ImVec4(192, 192, 192, 0), ImVec4(30, 144, 255, 255), 0.04f);
            ImGui::PushStyleColor(ImGuiCol_Button, IM_COL32(30, 144, 255, 255));
            ImGui::PushStyleColor(ImGuiCol_ButtonHovered, IM_COL32(30, 144, 255, 255));
            ImGui::PushStyleColor(ImGuiCol_ButtonActive, lerpedColor);
            ImGui::PushStyleColor(ImGuiCol_Border, borderColor); // 设置按钮边框颜色
            ImGui::PushStyleColor(ImGuiCol_Text, selectedTextColor);
        } else {
        ImVec4 lerpedColor = ImLerp(ImVec4(192, 192, 192, 0), ImVec4(30, 144, 255, 255), 0.04f);
          ImGui::PushStyleColor(ImGuiCol_Button, IM_COL32(192, 192, 192, 0)); // 白色透明度为0.8
          ImGui::PushStyleColor(ImGuiCol_ButtonHovered, IM_COL32(192, 192, 192, 0)); // 白色透明度为0.8
          ImGui::PushStyleColor(ImGuiCol_ButtonActive, lerpedColor); // 蓝色不透明
            ImGui::PushStyleColor(ImGuiCol_Border, borderColor); // 设置按钮边框颜色
            ImGui::PushStyleColor(ImGuiCol_Text, selectedTextColor);
        }
        
        if (ImGui::Button(options[i], width)) {
            *selectedIndex = i;
        }
        ImGui::PopStyleColor(5);
    }
    ImGui::PopStyleVar(3);
    ImGui::PopID();
}
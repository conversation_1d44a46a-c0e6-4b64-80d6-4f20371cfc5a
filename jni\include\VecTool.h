#ifndef VECTOOL_H
#define VECTOOL_H
#include "imgui.h"

struct VecTor2 {
    float x;
    float y;
    VecTor2() {
        this->x = 0;
        this->y = 0;
    }
    VecTor2(float x, float y) {
        this->x = x;
        this->y = y;
    }
	bool operator!=(const VecTor2 &Pos) {
		if (this->x != Pos.x || this->y != Pos.y){
			return true;
		}
		return false;
	}
    VecTor2 operator+(float v) const {
        return VecTor2(x + v, y + v);
    }
    VecTor2 operator-(float v) const {
        return VecTor2(x - v, y - v);
    }
    VecTor2 operator*(float v) const {
        return VecTor2(x * v, y * v);
    }
    VecTor2 operator/(float v) const {
        return VecTor2(x / v, y / v);
    }
    VecTor2& operator+=(float v) {
        x += v; y += v; return *this;
    }
    VecTor2& operator-=(float v) {
        x -= v; y -= v; return *this;
    }
    VecTor2& operator*=(float v) {
        x *= v; y *= v; return *this;
    }
    VecTor2& operator/=(float v) {
        x /= v; y /= v; return *this;
    }
    VecTor2 operator+(const VecTor2& v) const {
        return VecTor2(x + v.x, y + v.y);
    }
    VecTor2 operator-(const VecTor2& v) const {
        return VecTor2(x - v.x, y - v.y);
    }
    VecTor2 operator*(const VecTor2& v) const {
        return VecTor2(x * v.x, y * v.y);
    }
    VecTor2 operator/(const VecTor2& v) const {
        return VecTor2(x / v.x, y / v.y);
    }
    VecTor2& operator+=(const VecTor2& v) {
        x += v.x; y += v.y; return *this;
    }
    VecTor2& operator-=(const VecTor2& v) {
        x -= v.x; y -= v.y; return *this;
    }
    VecTor2& operator*=(const VecTor2& v) {
        x *= v.x; y *= v.y; return *this;
    }
    VecTor2& operator/=(const VecTor2& v) {
        x /= v.x; y /= v.y; return *this;
    }
};

struct VecTor3 {
    float x;
    float y;
    float z;
    VecTor3() {
        this->x = 0;
        this->y = 0;
        this->z = 0;
    }
    VecTor3(float x, float y, float z) {
        this->x = x;
        this->y = y;
        this->z = z;
    }
	bool operator!=(const VecTor3 &Pos) {
		if (this->x != Pos.x || this->y != Pos.y || this->z != Pos.z){
			return true;
		}
		return false;
	}
    VecTor3 operator+(float v) const {
        return VecTor3(x + v, y + v, z + v);
    }
    VecTor3 operator-(float v) const {
        return VecTor3(x - v, y - v, z - v);
    }
    VecTor3 operator*(float v) const {
        return VecTor3(x * v, y * v, z * v);
    }
    VecTor3 operator/(float v) const {
        return VecTor3(x / v, y / v, z / v);
    }
    VecTor3& operator+=(float v) {
        x += v; y += v; z += v; return *this;
    }
    VecTor3& operator-=(float v) {
        x -= v; y -= v; z -= v; return *this;
    }
    VecTor3& operator*=(float v) {
        x *= v; y *= v; z *= v; return *this;
    }
    VecTor3& operator/=(float v) {
        x /= v; y /= v; z /= v; return *this;
    }
    VecTor3 operator+(const VecTor3& v) const {
        return VecTor3(x + v.x, y + v.y, z + v.z);
    }
    VecTor3 operator-(const VecTor3& v) const {
        return VecTor3(x - v.x, y - v.y, z - v.z);
    }
    VecTor3 operator*(const VecTor3& v) const {
        return VecTor3(x * v.x, y * v.y, z * v.z);
    }
    VecTor3 operator/(const VecTor3& v) const {
        return VecTor3(x / v.x, y / v.y, z / v.z);
    }
    VecTor3& operator+=(const VecTor3& v) {
        x += v.x; y += v.y; z += v.z; return *this;
    }
    VecTor3& operator-=(const VecTor3& v) {
        x -= v.x; y -= v.y; z -= v.z; return *this;
    }
    VecTor3& operator*=(const VecTor3& v) {
        x *= v.x; y *= v.y; z *= v.z; return *this;
    }
    VecTor3& operator/=(const VecTor3& v) {
        x /= v.x; y /= v.y; z /= v.z; return *this;
    }
};

struct VecTor4 {
    float x;
    float y;
    float z;
    float w;
    VecTor4() {
        this->x = 0;
        this->y = 0;
        this->z = 0;
        this->w = 0;
    }
    VecTor4(float x, float y, float z, float w) {
        this->x = x;
        this->y = y;
        this->z = z;
        this->w = w;
    }
	bool operator!=(const VecTor4 &Pos) {
		if (this->x != Pos.x || this->y != Pos.y || this->z != Pos.z || this->w != Pos.w){
			return true;
		}
		return false;
	}
	VecTor4 operator+(float v) const {
        return VecTor4(x + v, y + v, z + v, w + v);
    }
    VecTor4 operator-(float v) const {
        return VecTor4(x - v, y - v, z - v, w - v);
    }
    VecTor4 operator*(float v) const {
        return VecTor4(x * v, y * v, z * v, w * v);
    }
    VecTor4 operator/(float v) const {
        return VecTor4(x / v, y / v, z / v, w / v);
    }
    VecTor4& operator+=(float v) {
        x += v; y += v; z += v; w += v; return *this;
    }
    VecTor4& operator-=(float v) {
        x -= v; y -= v; z -= v; w -= v; return *this;
    }
    VecTor4& operator*=(float v) {
        x *= v; y *= v; z *= v; w *= v; return *this;
    }
    VecTor4& operator/=(float v) {
        x /= v; y /= v; z /= v; w /= v; return *this;
    }
    VecTor4 operator+(const VecTor4& v) const {
        return VecTor4(x + v.x, y + v.y, z + v.z, w + v.w);
    }
    VecTor4 operator-(const VecTor4& v) const {
        return VecTor4(x - v.x, y - v.y, z - v.z, w - v.w);
    }
    VecTor4 operator*(const VecTor4& v) const {
        return VecTor4(x * v.x, y * v.y, z * v.z, w * v.w);
    }
    VecTor4 operator/(const VecTor4& v) const {
        return VecTor4(x / v.x, y / v.y, z / v.z, w / v.w);
    }
    VecTor4& operator+=(const VecTor4& v) {
        x += v.x; y += v.y; z += v.z; w += v.w; return *this;
    }
    VecTor4& operator-=(const VecTor4& v) {
        x -= v.x; y -= v.y; z -= v.z; w -= v.w; return *this;
    }
    VecTor4& operator*=(const VecTor4& v) {
        x *= v.x; y *= v.y; z *= v.z; w *= v.w; return *this;
    }
    VecTor4& operator/=(const VecTor4& v) {
        x /= v.x; y /= v.y; z /= v.z; w /= v.w; return *this;
    }
};

typedef struct _TOUCH_INFORMATION {
	float Scal;
	float TouchRadius;
	int TouchDeviceFile;
	VecTor2 TouchPoints;
	float floatswitch[15];
	bool TouchAimAtControl;
	VecTor2 TouchScreenSize;
	VecTor2 MouseCoordinate;
	VecTor2 AimingCoordinates;
	bool TouchOrientationControl;
} TOUCH_INFORMATION, *PTOUCH_INFORMATION;

typedef struct _RESOLUTION_INFORMATION {
	int Width;
	int Heiht;
	int Orientation;
	int ScreenWidth;
	int ScreenHeiht;
	int FixedScreenWidth;
	int FixedScreenHeiht;
} RESOLUTION_INFORMATION, *PRESOLUTION_INFORMATION;

typedef struct _IMGUISWITCH_INFORMATION {
	int intswitch[100];
	bool boolswitch[100];
	float floatswitch[100];
	ImColor colorswitch[100];
} IMGUISWITCH_INFORMATION, *PIMGUISWITCH_INFORMATION;


/*int isSpear(char *ClassName,char *name)
{
    if(strstr(ClassName, "BP_Sniper_M200"))//M200狙击枪
    {
        strcpy(name,"M200狙击枪");
        return 10400;			//
    }
    if(strstr(ClassName, "Rifle_M416"))
    {
        strcpy(name,"M416");
        return 10401;			// m416,m16,scar,qbz,m762,mk47
    }
    if(strstr(ClassName, "Rifle_M16A4"))
    {
        strcpy(name,"M16A4");
        return 10402;			//
    }
    if(strstr(ClassName, "Rifle_SCAR"))
    {
        strcpy(name,"SCAR");
        return 10403;			//
    }
    if(strstr(ClassName, "Rifle_QBZ"))
    {
        strcpy(name,"QBZ");
        return 10404;			//
    }
    if(strstr(ClassName, "Rifle_M762"))
    {
        strcpy(name,"M762");
        return 10405;			//
    }
    if(strstr(ClassName, "Rifle_Mk47"))
    {
        strcpy(name,"Mk47");
        return 10406;			//
    }
    if(strstr(ClassName, "Rifle_Groza"))
    {
        strcpy(name,"Groza");
        return 10407;			//
    }
    if(strstr(ClassName, "Sniper_AWM"))
    {
        strcpy(name,"AWM");
        return 10408;			//
    }
    if(strstr(ClassName, "Sniper_Kar98k"))
    {
        strcpy(name,"98k");
        return 10409;			//
    }
    if(strstr(ClassName, "Sniper_M24"))
    {
        strcpy(name,"M24");
        return 10410;			// M24
    }
    if(strstr(ClassName, "Sniper_Mini14"))
    {
        strcpy(name,"Mini14");
        return 10411;			//
    }
    if(strstr(ClassName, "Sniper_SKS"))
    {
        strcpy(name,"SKS");
        return 10412;			//
    }
    if(strstr(ClassName, "Sniper_QBU"))
    {
        strcpy(name,"QBU");
        return 10413;			//
    }
    if(strstr(ClassName, "MachineGun_TommyGun"))
    {
        strcpy(name,"Gun");
        return 10414;			//
    }
    if(strcmp(ClassName, "BP_Pistol_Flaregun_Wrapper_C") == 0)
    {
        strcpy(name,"信号枪");
        return 10415;			//信号枪
    }
    if(strstr(ClassName, "BP_Rifle_AUG"))
    {
        strcpy(name,"AUG");
        return 10416;			//
    }
    if(strstr(ClassName, "BP_Rifle_AKM"))//AK
    {
        strcpy(name,"AKM");
        return 10417;			//
    }
    if(strstr(ClassName, "BP_Sniper_Mosin_"))//莫辛纳狙击枪
    {
        strcpy(name,"莫辛纳狙击枪");
        return 10418;
    }	
    if(strstr(ClassName, "BP_Other_MG3"))//MG3
    {
        strcpy(name,"MG3");
        return 10419;			//
    }
    if(strstr(ClassName, "BP_Sniper_AMR"))//ARM狙击枪
    {
        strcpy(name,"ARM狙击枪");
        return 10420;			//
    }
    if(strstr(ClassName, "BP_Rifle_M417"))//M417
    {
        strcpy(name,"M417");
        return 10421;			//
    }
    if(strstr(ClassName, "BP_Other_HuntingBow"))//爆炸猎弓
    {
        strcpy(name,"爆炸猎弓");
        return 10422;			//
    }
    if(strstr(ClassName, "BP_Pistol_TMP"))//TMP-9手枪
    {
        strcpy(name,"TMP-9");
        return 10423;			//
    }
    if(strstr(ClassName, "Ammo_556mm"))
    {
        strcpy(name,"556子弹");
        return 10501;			// 556子弹
    }
    if(strstr(ClassName, "Ammo_762mm"))
    {
        strcpy(name,"762子弹");
        return 10502;
    }
    if(strstr(ClassName, "DJ_Large_EQ"))
    {
        strcpy(name,"步枪快速扩容");
        return 10601;			// 步枪快速扩容
    }
    if(strstr(ClassName, "DJ_Large_E"))
    {
        strcpy(name,"步枪扩容");
        return 10602;			// 步枪扩容
    }
    if(strstr(ClassName, "DJ_Sniper_EQ"))
    {
        strcpy(name,"狙击快速扩容");
        return 10603;			// 狙击快速扩容
    }
    if(strstr(ClassName, "DJ_Sniper_E"))
    {
        strcpy(name,"狙击扩容");
        return 10604;			// 狙击扩容
    }
    if(strstr(ClassName, "QK_Large_Suppressor"))
    {
        strcpy(name,"步枪消音");
        return 10605;			// 步枪消音
    }
    if(strstr(ClassName, "QK_Sniper_Suppressor"))
    {
        strcpy(name,"狙击消音");
        return 10606;			// 狙击消音
    }
    if(strstr(ClassName, "MZJ_3X"))
    {
        strcpy(name,"3倍");
        return 10607;			// 4倍
    }
    if(strstr(ClassName, "MZJ_4X"))
    {
        strcpy(name,"4倍");
        return 10608;			// 4倍
    }
    if(strstr(ClassName, "MZJ_6X"))
    {
        strcpy(name,"6倍");
        return 10609;			// 6倍
    }
    if(strstr(ClassName, "MZJ_8X_Ballistics"))
    {
        strcpy(name,"战术8倍");
        return 10611;			// 战术
    }
    if(strstr(ClassName, "MZJ_8X"))
    {
        strcpy(name,"8倍");
        return 10610;			// 8倍
    }
    //地上的燃烧瓶和手雷
    if(strstr(ClassName, "BP_Grenade_Shoulei_Weapon_Wrappe"))//
    {
        strcpy(name,"手雷");
        return 10503;			// 手雷
    }
    if(strstr(ClassName, "BP_Grenade_Burn_Weapon_Wrappe"))//
    {
        strcpy(name,"燃烧瓶");
        return 10504;			// 燃烧瓶
    }
    if(strstr(ClassName, "BP_Grenade_Weapon_Wrapper_Thermi"))//
    {
        strcpy(name,"铝热弹");
        return 10505;			// 铝热弹
    }
    if(strstr(ClassName, "BP_WEP_Pan_Pickup_"))
    {
        strcpy(name,"平底锅");
        return 10506;			//平底锅
    }
    if(strstr(ClassName, "BP_Other_Shield"))//突击盾牌
    {
        strcpy(name,"突击盾牌");
        return 10507;			//
    }
    return 0;
}*/

class ItemTypeIdentifier {
public:
    int ObjectTypes(const char* ClassName, char* Name) {
		
		// 载具 飞行 显示
		if (strstr(ClassName, "VH_Motorcycle_C") || strstr(ClassName, "VH_Motorcycle_1_C")) {
            strcpy(Name, "摩托车");
            return 10901;
        }
        if (strstr(ClassName, "VH_Scooter_C")) {
            strcpy(Name, "踏板摩托");
            return 10902;
        }
        if (strstr(ClassName, "VH_MotorcycleCart_C") || strstr(ClassName, "VH_MotorcycleCart_1_C") || strstr(ClassName, "BP_VH_Tuk_C") || strstr(ClassName, "BP_VH_Tuk_1_C")) {
            strcpy(Name, "三轮摩托");
            return 10903;
        }
        if (strstr(ClassName, "Buggy")) {
            strcpy(Name, "蹦蹦车");
            return 10904;
        }
        if (strstr(ClassName,  "Mirado_close_1")) {
            strcpy(Name, "肌肉跑车");
            return 10905;
        }
		if (strstr(ClassName,  "Mirado_open_1")) {
            strcpy(Name, "敞篷肌肉跑车");
            return 10906;
        }
		if (strstr(ClassName, "CoupeRB") || strstr(ClassName, "CoupeRB_1_C")) {
            strcpy(Name, "CoupeRB轿跑");
            return 10907;
        }
        if (strstr(ClassName, "Dacia")) {
            strcpy(Name, "轿车");
            return 10908;
        }
        if (strstr(ClassName,"PickUp")) {
            strcpy(Name, "皮卡车");
            return 10909;
        }
		if (strstr(ClassName, "Rony")) {
            strcpy(Name, "罗尼皮卡");
            return 109010;
        }
        if (strstr(ClassName, "UAZ")) {
            strcpy(Name, "吉普车");
            return 10911;
        }
        if (strstr(ClassName, "PG117")) {
            strcpy(Name, "快艇");
            return 10912;
        }
        if (strstr(ClassName, "AquaRail")) {
            strcpy(Name, "摩托艇");
            return 10913;
        }
        if (strstr(ClassName, "LadaNiva_")) {
            strcpy(Name, "雪地越野车");
            return 10914;
        }
        if (strstr(ClassName, "MiniBus")) {
            strcpy(Name, "迷你巴士");
            return 10915;
        }
		if (strstr(ClassName, "VH_Snowmobile_C") || strstr(ClassName, "VH_Snowmobile_1_C")) {
			strcpy(Name, "轻型雪地摩托");
			return 10916;
		}
		if (strstr(ClassName, "VH_Snowbike_C") || strstr(ClassName, "VH_Snowbike_1_C")) {
			strcpy(Name, "重型雪地摩托");
			return 10917;
		}
        if (strstr(ClassName, "VH_BRDM_C")) {
            strcpy(Name, "装甲车");
            return 10918;
        }
        if (strstr(ClassName, "AH6")) {
            strcpy(Name, "直升机");
            return 10919;
        }
        if (strstr(ClassName, "VH_Motorglider_")) {
            strcpy(Name, "滑翔机");
            return 10920;
        }
        if (strstr(ClassName, "BP_VH_Bigfoot_C")) {
            strcpy(Name, "大脚车");
            return 10921;
        }
        if (strstr(ClassName, "VH_StationWagon"))  {
            strcpy(Name, "旅行车");
            return 10922;
        }
		if (strstr(ClassName, "SciFi_Moto"))  {
            strcpy(Name, "啵啵车");
            return 10923;
        }
		if (strstr(ClassName, "VH_UTV_C"))  {
            strcpy(Name, "UTV");
            return 10924;
        }
		if (strstr(ClassName, "rado_close"))  {
            strcpy(Name, "四座跑车");
            return 10925;
        }
		if (strstr(ClassName, "licedTrain_C"))  {
            strcpy(Name, "磁吸小火车");
            return 10926;
        }
		if (strstr(ClassName, "VH_ATV1_C"))  {
            strcpy(Name, "全地形车");
            return 10927;
        }
		if (strstr(ClassName, "SportCar"))  {
            strcpy(Name, "敞篷跑车");
            return 10928;
        }
        if (strstr(ClassName, "VH_")) {
            strcpy(Name, "未收录");
            return 10929;
        }
        return 0;
    }
};

#endif

/*Zro开源*/
const char* equipment(int id) {
    switch(id) {
        case 9804001: return "1级头盔";
        case 9804002: return "2级头盔";
        case 9804003: return "3级头盔";
        case 9804004: return "4级头盔";
        case 9804005: return "4级头盔(铁爪)";
        case 9804006: return "4级头盔(黑鹰)";
        case 9804007: return "5级头盔";
        case 9804008: return "5级头盔(铁爪)";
        case 9804009: return "5级头盔(黑鹰)";
        case 9804010: return "6级头盔";
        case 9804011: return "6级头盔(铁爪)";
        case 9804012: return "6级头盔(黑鹰)";
        case 9804013: return "7级头盔";
        case 9804014: return "7级头盔(铁爪)";
        case 9804015: return "7级头盔(黑鹰)";
        case 9804016: return "头戴夜视仪";
        case 9804017: return "7级头盔";
        case 9804018: return "7级头盔(铁爪)";
        case 9804019: return "7级头盔(黑鹰)";
        case 9804020: return "机甲腿甲（冲刺技能）";
        case 9805001: return "1级防弹衣";
        case 9805002: return "2级防弹衣";
        case 9805003: return "3级防弹衣";
        case 9805004: return "4级防弹衣";
        case 9805005: return "4级防弹衣(铁爪)";
        case 9805006: return "4级防弹衣(黑鹰)";
        case 9805007: return "5级防弹衣";
        case 9805008: return "5级防弹衣(铁爪)";
        case 9805009: return "5级防弹衣(黑鹰)";
        case 9805010: return "6级防弹衣";
        case 9805011: return "6级防弹衣(铁爪)";
        case 9805012: return "6级防弹衣(黑鹰)";
        case 9805013: return "7级防弹衣";
        case 9805014: return "7级防弹衣(铁爪)";
        case 9805015: return "7级防弹衣(黑鹰)";
        case 9805016: return "7级防弹衣";
        case 9805017: return "7级防弹衣(铁爪)";
        case 9805018: return "7级防弹衣(黑鹰)";
        case 9805020: return "机甲防具";
        case 9805021: return "子物品-4级防弹衣";
        case 9805022: return "子物品-4级防弹衣(铁爪)";
        case 9805023: return "子物品-4级防弹衣(黑鹰)";
        case 9805024: return "子物品-5级防弹衣";
        case 9805025: return "子物品-5级防弹衣(铁爪)";
        case 9805026: return "子物品-5级防弹衣(黑鹰)";
        case 9805027: return "子物品-6级防弹衣";
        case 9805028: return "子物品-6级防弹衣(铁爪)";
        case 9805029: return "子物品-6级防弹衣(黑鹰)";
        case 9805030: return "子物品-7级防弹衣";
        case 9805031: return "子物品-7级防弹衣(铁爪)";
        case 9805032: return "子物品-7级防弹衣(黑鹰)";
        case 9805033: return "子物品-7级防弹衣";
        case 9805034: return "子物品-7级防弹衣(铁爪)";
        case 9805035: return "子物品-7级防弹衣(黑鹰)";
        case 9805098: return "子物品-7级防弹衣·特劳斯";
        case 9805099: return "7级防弹衣·特劳斯";
        case 9806001: return "1级背包";
        case 9806002: return "2级背包";
        case 9806003: return "3级背包";
        case 9806004: return "4级背包";
        case 9806005: return "4级背包(铁爪)";
        case 9806006: return "4级背包(黑鹰)";
        case 9806007: return "5级背包";
        case 9806008: return "5级背包(铁爪)";
        case 9806009: return "5级背包(黑鹰)";
        case 9806010: return "6级背包";
        case 9806011: return "6级背包(铁爪)";
        case 9806012: return "6级背包(黑鹰)";
        case 9806013: return "7级背包";
        case 9806014: return "7级背包(铁爪)";
        case 9806015: return "7级背包(黑鹰)";
        case 9806016: return "7级背包";
        case 9806017: return "7级背包(铁爪)";
        case 9806018: return "7级背包(黑鹰)";
        default: return "未知物品";
    }
}

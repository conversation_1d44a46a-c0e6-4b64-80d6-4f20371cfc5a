/*
搬运不留频道没母亲
https://t.me/CH_PUBG_1
@CH_PUBG_1
*/

#include "ImGuiELGS.h"
#include "safe_icon.h"
#include "aimi_icon.h"
#include "xm.h"
int GetEventCount()
{
    int count = 0;
    dirent *ptr = NULL;
    DIR *dir = opendir("/dev/input/");
    while ((ptr = readdir(dir)) != NULL)
    {
        if (strstr(ptr->d_name, "event"))
        {
            count++;
        }
    }
    return count ? count : -1;
}

int main(int PARAMETERONE, char *PARAMETERTWO[])
{

    ImGuiELGS *ELGS = new ImGuiELGS();
    if (ELGS != nullptr)
    {
        ELGS->ImGuiGetScreenInformation();
        if (!ELGS->ImGuiGetSurfaceWindow())
        {
            return 0;
        }
        ELGS->ImGuiInItialization();
    }
    input_event event;
    int eventcount = GetEventCount();
    if (eventcount <= 0)
    {
        cout << "\033[31;1m[-] 获取音量监听失败\033[30;1m" << endl;
        return 0;
    }
    int *volumedevicefilearray = (int *)malloc(eventcount * sizeof(int));
    for (int i = 0; i < eventcount; i++)
    {
        char inputfilepath[128] = "";
        sprintf(inputfilepath, "/dev/input/event%d", i);
        volumedevicefilearray[i] = open(inputfilepath, O_RDWR | O_NONBLOCK);
    }
    ELGS->xm = ELGS->texturereadsfile(xm, sizeof(xm));
    ELGS->yeaimi_icon = ELGS->texturereadsfile(yeaimi_icon, sizeof(yeaimi_icon));
    ELGS->noaimi_icon = ELGS->texturereadsfile(noaimi_icon, sizeof(noaimi_icon));
    ELGS->yesafe_icon = ELGS->texturereadsfile(yesafe_icon, sizeof(yesafe_icon));
    ELGS->nosafe_icon = ELGS->texturereadsfile(nosafe_icon, sizeof(nosafe_icon));

    Timer Thread_Time_Synchronization;
    Thread_Time_Synchronization.SetFps(ELGS->OneTimeFrame);
    Thread_Time_Synchronization.AotuFPS_init();

    // 设置CPU亲和性
    Thread_Time_Synchronization.SetCPUAffinity();

    while (ELGS->ShutImGuiProcess)
    {
        // 处理事件
        for (int i = 0; i < eventcount; i++)
        {
            memset(&event, 0, sizeof(input_event));
            read(volumedevicefilearray[i], &event, sizeof(event));
            if (event.type == EV_KEY && event.value == 1)
            {
                if (event.code == KEY_VOLUMEUP)
                {
                    ELGS->ImGuiWindowDisplay = true;
                }
                else if (event.code == KEY_VOLUMEDOWN)
                {
                    ELGS->ImGuiWindowDisplay = false;
                }
            }
        }

        // 渲染ImGui窗口
        ELGS->ImGuiWindowStar();
        ELGS->ImGuiWindowMenu();
        ELGS->ImGuiWindowDraw();
        ELGS->ImGuiWindowExit();

        // 只在必要时调用 SetFps

        Thread_Time_Synchronization.SetFps(ELGS->OneTimeFrame);

        Thread_Time_Synchronization.AotuFPS();
    }

    // 释放ImGui资源
    ELGS->ImGuiWindowRele();

    return 0;
}
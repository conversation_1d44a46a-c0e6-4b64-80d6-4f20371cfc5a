#include "ImGuiELGS.h"
#include "Custom.h"

bool Initializationsize;
const char *xxk1[] = {"主页", "其它"};
const char *xxk2[] = {"人物", "物资", "颜色"};
const char *xxk3[] = {"自瞄", "配置", "优先"};
int recordTime = 5; // 默认录制时间为 5 秒
void ExecuteCommand()
{
	std::string cmd = "screenrecord --time-limit " + std::to_string(recordTime) + " /sdcard/video.mp4";
	std::system(cmd.c_str()); // 将 std::string 转换为 C 风格字符串
}

void ImGuiELGS::ImGuiWindowMenu()
{

	if (initializeaimi)
	{
		static bool AimiDown = false;
		static ImVec2 AimiPos = {0, 0};
		if (ImGui::Begin("Aimi", &initializeaimi, ImGuiWindowFlags_NoDecoration | ImGuiWindowFlags_NoBackground))
		{
			if (ImGui::IsItemActive())
			{
				if (!AimiDown)
				{
					AimiDown = true;
					AimiPos = ImGui::GetWindowPos();
				}
			}
			else if (AimiDown)
			{
				AimiDown = false;
				if (AimiPos.x == ImGui::GetWindowPos().x && AimiPos.y == ImGui::GetWindowPos().y)
				{
					imguiswitch_information.intswitch[59] = !imguiswitch_information.intswitch[59];
				}
			}
			if (imguiswitch_information.intswitch[59])
			{
				ImGui::Image(noaimi_icon, ImVec2{110, 110}, ImVec2{0, 0}, ImVec2{1, 1});
			}
			else if (!imguiswitch_information.intswitch[59])
			{
				ImGui::Image(yeaimi_icon, ImVec2{110, 110}, ImVec2{0, 0}, ImVec2{1, 1});
			}
		}
	}

	ImVec2 WindowPos = ImVec2(1030.f, 780.f);
	ImGui::SetWindowSize("Broken", WindowPos);
	CenterWindow.x = (resolution_information.ScreenWidth - WindowPos.x) / 2.f;
	CenterWindow.y = (resolution_information.ScreenHeiht - WindowPos.y) / 2.f;

	if (ShutImGuiProcess)
	{
		ImGui::GetStyle().Colors[ImGuiCol_Border] = ImColor(ImCandy::Rainbow(static_ratio, 0.0010));
		if (Custom::Begin("Broken", &ImGuiWindowDisplay, ImGuiWindowFlags_NoDecoration))
		{
			ImVec2 WindowPos = ImGui::GetWindowPos();

			switch (Tab)
			{
			case 1:
			{

				ImGui::SetCursorPos(ImVec2(276, 15));
				if (ImGui::BeginChild("SubTab##1", ImVec2(739, 100), false))
				{
					ImGui::SetCursorPos(ImVec2(10, 10));
					Custom::RadioGroup("主页选项", &SubTab1, xxk1, 2, ImVec2(247, 80));
				}
				ImGui::EndChild();
				switch (SubTab1)
				{

				case 0:
				{
					ImGui::SetCursorPos(ImVec2(276, 138));
					Custom::begin_child("主页面板", {739, 623});
					{
						// DrawGradientText("Hello, ImGui!", ImVec4(1.0f, 0.0f, 0.0f, 1.0f), ImVec4(0.0f, 0.0f, 1.0f, 1.0f));
						ImGui::Text("设备分辨率: %dx%d", resolution_information.ScreenWidth, resolution_information.ScreenHeiht);
						ImGui::Text("ImGuiFrames: %0.2fFPS %0.2fms", ImGui::GetIO().Framerate, 1000.f / ImGui::GetIO().Framerate);
						if (ImGui::SliderInt("帧数设置", &OneTimeFrame, 60, 144))
						{
							SaveFile(".HP_SIVE");
						}

						ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.68f, 0.85f, 0.90f, 1.0f));
						if (!initializedraw)
						{
							if (ImGui::Button("初始化运行", {710, 88}))
							{
								initializedraw = true;
								if (initializedraw)
								{
									GetPid("com.tencent.tmgp.pubgmhd");
									pid = Pid;

									initialize();
									ModulesBase[0] = GetModuleAddressTwo("libUE4.so");
									基址头 = driver1->get_module_base("libUE4.so");
								}
							}
						}
						else
						{
							if (ImGui::Button("停止绘制", {710, 88}))
							{
								initializedraw = false;
							}
						}
						ImGui::PopStyleColor();
						if (ImGui::Button("退出程序", {370, 88}))
						{
							exit(1);
						}
					}
					Custom::end_child();
					break;
				}
				case 1:
				{
					ImGui::SetCursorPos(ImVec2(276, 138));
					Custom::begin_child("其他功能", {739, 623});
					{

						if (ImGui::Button("视频录制"))
						{
							std::thread commandThread(ExecuteCommand); // 分离线程，允许它在后台运行，不阻塞主线程
							commandThread.detach();
						}
						ImGui::SliderInt("录制时长", &recordTime, 1, 180);
						if (ImGui::CollapsingHeader("debug"))
						{
							ImGui::TextColored(ImColor(0, 255, 0, 225), "世界: 0x%lX", WorldAddress);
							ImGui::TextColored(ImColor(0, 255, 0, 225), "列阵: 0x%lX", ArrayAddress);
							ImGui::TextColored(ImColor(0, 255, 0, 225), "自身: 0x%lX", SelfAddress);

							ImGui::BulletText(("开镜 %d"), IsCamera);
							ImGui::SameLine(); // 在同行
							ImGui::BulletText(("开火 %d"), IsFire);

							ImGui::BulletText(("自己动作 %d"), 自身动作);
							// ImGui::BulletText(("IsGameStart %d"),IsGameStart);
							ImGui::BulletText(("队伍 %d"), OwnTeam);
							ImGui::BulletText(("数量 %d"), ArraysCount);
						}
						ImGui::SliderFloat("圆", &yuan, -100.f, 150.f);
						ImGui::SliderFloat("左", &zuo, -100.f, 150.f);
						ImGui::SliderFloat("右", &you, -100.f, 150.f);
					}
					Custom::end_child();
					break;
				}
				}
				break;
			}

			case 2:
			{
				ImGui::SetCursorPos(ImVec2(276, 15));
				if (ImGui::BeginChild("SubTab##1", ImVec2(739, 100), false))
				{
					ImGui::SetCursorPos(ImVec2(10, 10));
					Custom::RadioGroup("主页选项", &SubTab2, xxk2, 3, ImVec2(247, 80));
				}
				ImGui::EndChild();

				switch (SubTab2)
				{

				case 0:
				{

					ImGui::SetCursorPos(ImVec2(276, 138));
					Custom::begin_child("人物绘制", {739, 623});

					ImGui::Checkbox("忽略人机", &aigl);
					ImGui::SameLine();
					ImGui::Checkbox("观透", &观透);

					if (ImGui::Checkbox("绘制骨骼", &imguiswitch_information.boolswitch[0]))
					{
						SaveFile(".PUBG_SIVE");
					}
					ImGui::SameLine();
					if (ImGui::Checkbox("绘制方框", &imguiswitch_information.boolswitch[1]))
					{
						SaveFile(".PUBG_SIVE");
					}
					ImGui::SameLine();
					if (ImGui::Checkbox("绘制射线", &imguiswitch_information.boolswitch[2]))
					{
						SaveFile(".PUBG_SIVE");
					}
					if (ImGui::Checkbox("绘制雷达", &imguiswitch_information.boolswitch[3]))
					{
						SaveFile(".PUBG_SIVE");
					}
					ImGui::SameLine();
					if (ImGui::Checkbox("绘制背敌", &imguiswitch_information.boolswitch[4]))
					{
						SaveFile(".PUBG_SIVE");
					}
					ImGui::SameLine();
					if (ImGui::Checkbox("绘制血条", &imguiswitch_information.boolswitch[6]))
					{
						SaveFile(".PUBG_SIVE");
					}

					if (ImGui::Checkbox("绘制距离", &imguiswitch_information.boolswitch[7]))
					{
						SaveFile(".PUBG_SIVE");
					}
					ImGui::SameLine();
					if (ImGui::Checkbox("绘制名称", &imguiswitch_information.boolswitch[8]))
					{
						SaveFile(".PUBG_SIVE");
					}
					ImGui::SameLine();
					if (ImGui::Checkbox("绘制手持", &imguiswitch_information.boolswitch[9]))
					{
						SaveFile(".PUBG_SIVE");
					}
					if (ImGui::CollapsingHeader("样式设置"))
					{
						if (ImGui::Combo("血条UI", &血条, "圆血条\0经典血条\0扁血条\0"))
						{
							SaveFile(".HP_SIVE");
						}
					}

					/*	if (imguiswitch_information.boolswitch[0]) {
							ImGui::ItemSize({0.f, 5.f});
							if (ImGui::SliderInt("骨骼显示距离设置", &imguiswitch_information.intswitch[90], 30, 500)) {
								SaveFile(".PUBG_SIVE");
							}
						} */
					ImGui::ItemSize({0.f, 5.f});
					if (ImGui::SliderFloat("画笔粗细", &imguiswitch_information.floatswitch[57], 1.f, 5.f, "%.2f"))
					{
						SaveFile(".PUBG_SIVE");
					}
					if (ImGui::CollapsingHeader("雷达设置"))
					{
						if (ImGui::SliderFloat("雷达左右调整", &imguiswitch_information.floatswitch[40], -1000.f, 1000.f, "%.2f"))
						{
							SaveFile(".HP_SIVE");
						}
						if (ImGui::SliderFloat("雷达上下调整", &imguiswitch_information.floatswitch[41], -1000.f, 1000.f, "%.2f"))
						{
							SaveFile(".HP_SIVE");
						}
						if (ImGui::SliderFloat("雷达缩放调整", &imguiswitch_information.floatswitch[42], 0.f, 200.f, "%.2f"))
						{
							SaveFile(".HP_SIVE");
						}
						if (ImGui::SliderFloat("雷达大小调整", &imguiswitch_information.floatswitch[43], 100.f, 300.f, "%.2f"))
						{
							SaveFile(".HP_SIVE");
						}
					}
					Custom::end_child();
					break;
				}
				case 1:
				{
					ImGui::SetCursorPos(ImVec2(276, 138));
					Custom::begin_child("其它绘制", {739, 623});
					if (ImGui::Checkbox("绘制名称", &imguiswitch_information.boolswitch[11]))
					{
						SaveFile(".PUBG_SIVE");
					}
					if (ImGui::Checkbox("绘制油量", &imguiswitch_information.boolswitch[12]))
					{
						SaveFile(".PUBG_SIVE");
					}
					if (ImGui::Checkbox("绘制血量", &imguiswitch_information.boolswitch[13]))
					{
						SaveFile(".PUBG_SIVE");
					}
					ImGui::Separator();

					// if(ImGui::Checkbox("炫酷颜色<娱乐>", &无敌炫酷));

					// if(ImGui::Checkbox("类名打印", &类名));
					if (ImGui::Checkbox("显示5.56", &imguiswitch_information.boolswitch[53]))
					{
						SaveFile(".HP_SIVE");
					}
					ImGui::SameLine();
					if (ImGui::Checkbox("显示7.62", &imguiswitch_information.boolswitch[54]))
					{
						SaveFile(".HP_SIVE");
					}
					ImGui::SameLine();
					if (ImGui::Checkbox("显示药品", &imguiswitch_information.boolswitch[55]))
					{
						SaveFile(".HP_SIVE");
					}
					if (ImGui::Checkbox("显示信号枪/召回", &imguiswitch_information.boolswitch[588]))
					{
						SaveFile(".HP_SIVE");
					}
					if (ImGui::Checkbox("显示步枪", &imguiswitch_information.boolswitch[56]))
					{
						SaveFile(".HP_SIVE");
					}
					ImGui::SameLine();
					if (ImGui::Checkbox("显示冲锋枪", &showSubmachine))
					{
						SaveFile(".HP_SIVE");
					}
					ImGui::SameLine();
					if (ImGui::Checkbox("显示狙击枪", &showSniper))
					{
						SaveFile(".HP_SIVE");
					}
					if (ImGui::Checkbox("显示倍镜", &showMirror))
					{
						SaveFile(".HP_SIVE");
					}
					ImGui::SameLine();
					if (ImGui::Checkbox("显示头甲", &showArmor))
					{
						SaveFile(".HP_SIVE");
					}
					ImGui::SameLine();
					if (ImGui::Checkbox("显示骨灰盒", &imguiswitch_information.boolswitch[589]))
					{
						SaveFile(".HP_SIVE");
					}

					if (ImGui::Checkbox("显示空投", &imguiswitch_information.boolswitch[590]))
					{
						SaveFile(".HP_SIVE");
					}
					ImGui::Checkbox("地铁", &地铁);
					//	ImGui::Checkbox("类名", &类名);

					Custom::end_child();
					break;
				}
				case 2:
				{
					ImGui::SetCursorPos(ImVec2(276, 138));
					Custom::begin_child("颜色设置", {739, 623});
					{
						if (ImGui::ColorEdit4("方框颜色", imguiswitch_information.colorswitch[7], ImGuiColorEditFlags_DisplayHex))
						{
							SaveFile(".HP_SIVE");
						}
						if (ImGui::ColorEdit4("骨骼颜色", imguiswitch_information.colorswitch[8], ImGuiColorEditFlags_DisplayHex))
						{
							SaveFile(".HP_SIVE");
						}
						if (ImGui::ColorEdit4("射线颜色", imguiswitch_information.colorswitch[9], ImGuiColorEditFlags_DisplayHex))
						{
							SaveFile(".HP_SIVE");
						}
						if (ImGui::ColorEdit4("距离颜色", imguiswitch_information.colorswitch[0], ImGuiColorEditFlags_DisplayHex))
						{
							SaveFile(".HP_SIVE");
						}
						if (ImGui::ColorEdit4("名称颜色", imguiswitch_information.colorswitch[1], ImGuiColorEditFlags_DisplayHex))
						{
							SaveFile(".HP_SIVE");
						}
						if (ImGui::ColorEdit4("手持颜色", imguiswitch_information.colorswitch[2], ImGuiColorEditFlags_DisplayHex))
						{
							SaveFile(".HP_SIVE");
						}
					}
					Custom::end_child();
					break;
				}
				}

				break;
			}
			case 3:
			{
				ImGui::SetCursorPos(ImVec2(276, 15));
				if (ImGui::BeginChild("SubTab##1", ImVec2(739, 100), false))
				{
					ImGui::SetCursorPos(ImVec2(10, 10));
					Custom::RadioGroup("主页选项", &SubTab3, xxk3, 3, ImVec2(247, 80));
				}
				ImGui::EndChild();

				switch (SubTab3)
				{
				case 0:
				{
					ImGui::SetCursorPos(ImVec2(276, 138));
					Custom::begin_child("触摸自瞄", {739, 623});
					{
						ImGui::Checkbox("开启触摸自瞄", &initializeaimi);
						imguiswitch_information.intswitch[55] = 1;
						ImGui::Checkbox("动态范围", &dynamic);

						if (ImGui::Checkbox("显示触摸区域", &imguiswitch_information.boolswitch[50]))
						{
							SaveFile(".HP_SIVE");
						}
						ImGui::SameLine();
						if (ImGui::Checkbox("显示自瞄圆圈", &imguiswitch_information.boolswitch[51]))
						{
							SaveFile(".HP_SIVE");
						}
						ImGui::SameLine();
						if (ImGui::Checkbox("显示自瞄射线", &imguiswitch_information.boolswitch[52]))
						{
							SaveFile(".HP_SIVE");
						}
					}

					ImGui::Separator();
					if (ImGui::CollapsingHeader("触摸参数"))
					{
						if (ImGui::SliderFloat("触摸位置左右", &touch_information.TouchPoints.y, 0.f, 1.f, "%.2f"))
						{
							touch_information.TouchOrientationControl = true;
							SaveFile(".HP_SIVE");
						}
						if (ImGui::SliderFloat("触摸位置上下", &touch_information.TouchPoints.x, 0.f, 1.f, "%.2f"))
						{
							touch_information.TouchOrientationControl = true;
							SaveFile(".HP_SIVE");
						}
						if (ImGui::SliderFloat("触摸范围调整", &touch_information.TouchRadius, 0.03f, 0.08f, "%.2f"))
						{
							touch_information.TouchOrientationControl = true;
							SaveFile(".HP_SIVE");
						}
						if (ImGui::SliderFloat("触摸帧数设置", &touch_information.floatswitch[0], 500.f, 2000.f, "%.2f"))
						{
							SaveFile(".HP_SIVE");
						}
					}
					Custom::end_child();
					break;
				}
				case 1:
				{
					ImGui::SetCursorPos(ImVec2(276, 138));
					Custom::begin_child("触摸配置", {739, 623});
					{
						if (ImGui::CollapsingHeader("触摸通用设置"))
						{
							if (ImGui::SliderFloat("触摸左右滑动速度", &touch_information.floatswitch[1], 0.01f, 0.85f, "%.2f"))
							{
								SaveFile(".HP_SIVE");
							}
							if (ImGui::SliderFloat("触摸上下滑动速度", &touch_information.floatswitch[2], 0.01f, 0.85f, "%.2f"))
							{
								SaveFile(".HP_SIVE");
							}
							if (ImGui::SliderFloat("触摸屏幕滑动速度", &touch_information.floatswitch[3], 0.01f, 0.85f, "%.2f"))
							{
								touch_information.TouchOrientationControl = true;
								SaveFile(".HP_SIVE");
							}
						}
						ImGui::Separator();

						if (ImGui::CollapsingHeader("自瞄设置"))
						{
							if (ImGui::SliderFloat("自瞄范围调整", &imguiswitch_information.floatswitch[50], 80.f, 800.f, "%.2f"))
							{
								SaveFile(".HP_SIVE");
							}
							if (ImGui::SliderFloat("自瞄距离调整", &imguiswitch_information.floatswitch[51], 50.f, 500.f, "%.2f"))
							{
								SaveFile(".HP_SIVE");
							}
							if (ImGui::SliderFloat("自瞄预判调整", &imguiswitch_information.floatswitch[52], 0.01f, 0.25f, "%.2f"))
							{
								SaveFile(".HP_SIVE");
							}
							/*	if (ImGui::SliderFloat("腰射距离调整", &imguiswitch_information.floatswitch[56], 10.f, 500.f, "%.2f")) {
									SaveFile(".HP_SIVE");
								}   */
						}

						ImGui::Separator();

						if (ImGui::CollapsingHeader("压枪设置"))
						{
							if (ImGui::SliderFloat("站着压枪调整", &imguiswitch_information.floatswitch[53], 1.f, 9.f, "%.2f"))
							{
								SaveFile(".HP_SIVE");
							}
							if (ImGui::SliderFloat("蹲着压枪调整", &imguiswitch_information.floatswitch[54], 1.f, 9.f, "%.2f"))
							{
								SaveFile(".HP_SIVE");
							}
							if (ImGui::SliderFloat("趴着压枪调整", &imguiswitch_information.floatswitch[55], 1.f, 9.f, "%.2f"))
							{
								SaveFile(".HP_SIVE");
							}

							if (ImGui::SliderFloat("200米站着压枪调整", &ycsz, 1.f, 9.f, "%.2f"))
							{
								SaveFile(".HP_SIVE");
							}
							if (ImGui::SliderFloat("200米蹲着压枪调整", &ycsd, 1.f, 9.f, "%.2f"))
							{
								SaveFile(".HP_SIVE");
							}
							if (ImGui::SliderFloat("200米趴着压枪调整", &ycsp, 1.f, 9.f, "%.2f"))
							{
								SaveFile(".HP_SIVE");
							}
						}
					}
					Custom::end_child();
					break;
				}
				case 2:
				{
					ImGui::SetCursorPos(ImVec2(276, 138));
					Custom::begin_child("优先设置", {739, 623});
					{
						if (ImGui::Combo("自瞄瞄准调整", &imguiswitch_information.intswitch[50], "开火自瞄\0"))
						{
							SaveFile(".HP_SIVE");
						}

						if (ImGui::Combo("自瞄部位优先", &imguiswitch_information.intswitch[51], "瞄准头部\0\r\瞄准胸部\0\r暂未添加\0\0"))
						{
							SaveFile(".HP_SIVE");
						}

						if (ImGui::Combo("自瞄倒地优先", &imguiswitch_information.intswitch[52], "倒地瞄准\0\r\倒地不喵\0\r暂未添加\0\0"))
						{
							SaveFile(".HP_SIVE");
						}

						if (ImGui::Combo("自瞄选人优先", &imguiswitch_information.intswitch[53], "准心最近\0\r\距离最近\0\r暂未添加\0\0"))
						{
							SaveFile(".HP_SIVE");
						}

						if (ImGui::Combo("自瞄人机调整", &imguiswitch_information.intswitch[54], "瞄准人机\0\r\不喵人机\0\r暂未添加\0\0"))
						{
							SaveFile(".HP_SIVE");
						}
					}
					Custom::end_child();
					break;
				}
				}
				break;
			}
			}

			ImGui::SetCursorPos(ImVec2(15, 15));
			if (ImGui::BeginChild("MainChild", {240, 210}))
			{
				ImGui::SetCursorPos(ImVec2(30, 15));
				ImGui::Image(xm, ImVec2{175.f, 170.f}, ImVec2{0, 0}, ImVec2{1, 1});
			}
			ImGui::EndChild();

			ImGui::SetCursorPos(ImVec2(15, 235));
			if (ImGui::BeginChild("MainChild2", {240, 110}))
			{
				ImGui::SetCursorPos(ImVec2(50, 30));
				if (Pid == 0 || Pid < 0 || !initializedraw)
				{

					ImGui::SetCursorPos(ImVec2(15, 37));
					ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.0f, 0.0f, 1.0f));

					ImGui::BulletText("未初始化");

					ImGui::PopStyleColor();
				}
				else
				{

					ImGui::SetCursorPos(ImVec2(15, 37));
					ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(124.0f / 255.0f, 252.0f / 255.0f, 0.0f, 204.0f / 255.0f));

					ImGui::BulletText("已初始化");

					ImGui::PopStyleColor();
				}
			}
			ImGui::EndChild();

			ImGui::SetCursorPos(ImVec2(15, 360));
			if (ImGui::BeginChild("MainChild3", {240, 400}))
			{
				ImGui::SetCursorPos(ImVec2(0, 60));
				if (Custom::tab("E", "主页", Tab == 1))
					Tab = 1;
				if (Custom::tab("B", "绘制", Tab == 2))
					Tab = 2;
				if (Custom::tab("A", "自瞄", Tab == 3))
					Tab = 3;
			}
			ImGui::EndChild();
			Custom::End();
		}
	}
	if (CenterWindow.x != 0 && CenterWindow.y != 0 && !Initializationsize)
	{
		ImGui::SetWindowPos("Broken", CenterWindow);
		Initializationsize = true;
	}
}

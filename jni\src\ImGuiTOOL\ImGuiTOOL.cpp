#include "ImGuiTOOL.h"

int ImGuiTOOL::GetMainDeviceNumber(char *path) {
    FILE* file = fopen(path, "rb");
    if (!file) {
		return 0;
	}
    int zero = 0;
	int neko = 0;
    char line[256] = "";
    while (fgets(line, sizeof(line), file)) {
        if (sscanf(line, "%d:%d", &neko, &zero) == 2) {
			if (zero == 0) {
				fclose(file);
            	return neko;
			}
        }
    }
    fclose(file);
    return 0;
}

int ImGuiTOOL::ContainsDigit(char *str) {
    for (size_t i = 0; i < strlen(str); ++i) {
		if (isdigit(static_cast<unsigned char>(str[i]))) {
			return 1;
		}
	}
    return 0;
}

int ImGuiTOOL::FindAndCreateDeviceNode() {
    DIR *dir = opendir("/sys/devices/virtual/");
    if (dir == NULL) {
        return -1;
    }
	char command[1024] = "";
	char devNode[1024] = "";
	struct dirent *deviceDir;
	char devicePath[1024] = "";
	struct dirent *subDeviceDir;
	char subDevicePath[1024] = "";
    while ((deviceDir = readdir(dir)) != NULL) {
        snprintf(devicePath, sizeof(devicePath), "/sys/devices/virtual/%s/", deviceDir->d_name);
        DIR *subdir = opendir(devicePath);
        if (subdir != NULL) {
            while ((subDeviceDir = readdir(subdir)) != NULL) {
                if (ContainsDigit(subDeviceDir->d_name)) {
                    continue;
                }
                if (subDeviceDir->d_type == DT_DIR) {
                    if (strlen(subDeviceDir->d_name) == 6 && !strchr(subDeviceDir->d_name, '.') && !strchr(subDeviceDir->d_name, '_') && !strchr(subDeviceDir->d_name, '-') && !strchr(subDeviceDir->d_name, ':') && !strchr(subDeviceDir->d_name, 'm')) {
                        snprintf(subDevicePath, sizeof(subDevicePath), "/sys/class/%s/%s/dev", deviceDir->d_name, subDeviceDir->d_name);
                        int mainDeviceNumber = GetMainDeviceNumber(subDevicePath);
                        if (mainDeviceNumber == 0) {
                            continue;
                        }
                        if (strcmp(deviceDir->d_name, subDeviceDir->d_name) != 0) {
							snprintf(command, sizeof(command), "su -c mknod /dev/%s c %d 0", subDeviceDir->d_name, mainDeviceNumber);
							exec(command);
                            snprintf(devNode, sizeof(devNode), "/dev/%s", subDeviceDir->d_name);
							printf("%s",devNode);
                            int fileDescriPtion = open(devNode, O_RDWR);
                            if (fileDescriPtion <= 0) {
                                closedir(subdir);
                                closedir(dir);
                                return -1;
                            }
                            if (unlink(devNode) == 0) {
                                closedir(subdir);
                                closedir(dir);
                                return fileDescriPtion;
                            }
                        }
                    }
                }
            }
            closedir(subdir);
        }
    }
    closedir(dir);
    return -1;
}

/*
#include <unistd.h>

int ImGuiTOOL::FindAndCreateDeviceNode() {
    DIR *dir = opendir("/sys/devices/virtual/");
    if (dir == NULL) {
        return -1;
    }
    char command[1024] = "";
    char devNode[1024] = "";
    struct dirent *deviceDir;
    char devicePath[1024] = "";
    struct dirent *subDeviceDir;
    char subDevicePath[1024] = "";
    while ((deviceDir = readdir(dir)) != NULL) {
        snprintf(devicePath, sizeof(devicePath), "/sys/devices/virtual/%s/", deviceDir->d_name);
        DIR *subdir = opendir(devicePath);
        if (subdir != NULL) {
            while ((subDeviceDir = readdir(subdir)) != NULL) {
                if (ContainsDigit(subDeviceDir->d_name)) {
                    continue;
                }
                if (subDeviceDir->d_type == DT_DIR) {
                    if (strlen(subDeviceDir->d_name) == 6 && !strchr(subDeviceDir->d_name, '.') &&
                        !strchr(subDeviceDir->d_name, '_') && !strchr(subDeviceDir->d_name, '-') &&
                        !strchr(subDeviceDir->d_name, ':')) {
                        snprintf(subDevicePath, sizeof(subDevicePath), "/sys/class/%s/%s/dev",
                                 deviceDir->d_name, subDeviceDir->d_name);
                        int mainDeviceNumber = GetMainDeviceNumber(subDevicePath);
                        printf("%s\n",subDeviceDir->d_name);
                        if (mainDeviceNumber == 0) {
                            continue;
                        }
                        if (strcmp(deviceDir->d_name, subDeviceDir->d_name) != 0) {
                            snprintf(command, sizeof(command), "su -c mknod /dev/%s c %d 0",
                                     subDeviceDir->d_name, mainDeviceNumber);
                            printf("成功%s\n",subDeviceDir->d_name);
                            exec(command);
                            int fileDescriPtion = open(devNode, O_RDWR);
                            if (fileDescriPtion <= 0) {
                                closedir(subdir);
                                closedir(dir);
                                return -1;
                            }
                            if (unlink(devNode) == 0) {
                                closedir(subdir);
                                closedir(dir);
                                return fileDescriPtion;
                            }
                        }
                    }
                }
            }
            closedir(subdir);
        }
    }
    closedir(dir);
    return -1;
}
*/
